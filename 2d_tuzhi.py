# -*- coding: utf-8 -*-
import MySQL

import base64

import Lite
from Function import *
import configparser




config = configparser.ConfigParser()
try:
    cfgfile = open('config.ini', 'r')
    config.readfp(cfgfile)
    zip_db_host = config.get('zip', 'zip_db_host')
    zip_user = config.get('zip', 'zip_db_user')
    zip_db_password = config.get('zip', 'zip_db_password')
    zip_db_db = config.get('zip', 'zip_db_db')

    tuzhi_db_host = config.get('tuzhi', 'tuzhi_db_host')
    tuzhi_user = config.get('tuzhi', 'tuzhi_db_user')
    tuzhi_db_password = config.get('tuzhi', 'tuzhi_db_password')
    tuzhi_db_db = config.get('tuzhi', 'tuzhi_db_db')
    # 临时解压目录，谨慎填写，否则可能会删除正常文件
    zipTempDir = config.get('path', 'zipTempDir')
    downDir = config.get('path', 'downDir')

    access_key = config.get('qiniu', 'access_key')
    secret_key = config.get('qiniu', 'secret_key')
    bucket_name = config.get('qiniu', 'bucket_name')
    bucket_domain = config.get('qiniu', 'bucket_domain')

    cos_access_key = config.get('cos', 'access_key')
    cos_secret_key = config.get('cos', 'secret_key')
    cos_bucket_name = config.get('cos', 'bucket_name')
except:
    print('读取配置文件错误！')
    exit()

def mysqls():
    n = MySQL.MySQL(tuzhi_db_host, tuzhi_user, tuzhi_db_password, 3306)
    n.selectDb(tuzhi_db_db)
    return n

def zip_mysqls():
    n = MySQL.MySQL(zip_db_host, zip_user, zip_db_password, 3306)
    n.selectDb(zip_db_db)
    return n

def mfcad_Lite():
    mfcad_lite = Lite.Lite()
    return mfcad_lite


#修改图纸数据表字段状态
def update_isedrawings(n, status, tuid):
    n.update('v9_dede_soft', {'isedrawings': status}, 'id=' + str(tuid))
    n.commit()

#添加轻量化数据
def update_task(data, tuid):
    z = zip_mysqls()
    for d in data:
        imgurl = 'D:/3DLite/minioSever/severData/openapi/' + base64.b64decode(d['imgURL']).split('/openapi/')[-1]
        d['imgURL'] = 'V7/'+str(tuid)+'/'+str(tuid)+'.png'
        file_result = cos_upload(access_key,secret_key,bucket_name,imgurl, d['imgURL'])
        if file_result == False:
            d['imgURL'] = '上传失败,请检查程序是否异常!'

        liteurl = 'D:/3DLite/minioSever/severData/openapi/tempFile/download/'+ str(base64.b64decode(d['outputNdsFiles']).split('?X-Amz-Algorithm').split('/')[-1])
        d['outputNdsFiles'] = 'V7/'+str(tuid)+'/'+str(tuid)+'.zip'
        file_result = cos_upload(access_key,secret_key,bucket_name,liteurl, d['outputNdsFiles'])
        if file_result == False:
            d['outputNdsFiles'] = '上传失败,请检查程序是否异常!'
        z.insert('3d_taskdetail',{'inputFileName':d['inputFileName'],'convertTime':d['convertTime'],'inputFileSize':d['inputFileSize'],'outputFileSize':d['outputFileSize'],'convertPercent':d['convertPercent'],'triangleNum':d['triangleNum'],'taskType':d['taskType'],'status':d['status'],'imgURL':d['imgURL'],'outputNdsFiles':d['outputNdsFiles'],'errorDescription':d['errorDescription'],'errorDetail':d['errorDetail'],'errorType':d['errorType']})
    z.commit()



# 列出符合条件的所有文件，并下载
def listAll(bucket_name):
    n = mysqls()
    mfcad_lite = mfcad_Lite()

    n.query('SELECT s.id AS tuid,REPLACE (d.test, "tuzhi/", "") AS filename FROM v9_dede_soft s INNER JOIN v9_dede_soft_data d ON s.id = d.id WHERE s.status = 1 and s.format_type = 2 and s.isedrawings = 0 and d.test <> '' limit 3')
    tulist = n.fetchAll()
    if not tulist or len(tulist[0]['filename']) == 0:
        print('二维图纸没有待转换任务!!!')
        return
    
    for tudata in tulist:
        update_isedrawings(n, 1, tudata['tuid'])
        tuzhi_info = get_file_from_qiniu(access_key, secret_key, bucket_domain, downDir, bucket_name, tudata['filename'], tudata['tuid'])
        if not tuzhi_info['file_status']:
            update_isedrawings(n, 3, tudata['tuid'])
            continue

        taskids = []
        batch = True
        #判断文件名是否是压缩包
        if iszip(tudata['filename']):
            datainfo =mfcad_lite.BatchInsertTask(tuzhi_info['tuzhi_url'])
        else:
            batch = False
            datainfo =mfcad_lite.InsertTask(tuzhi_info['tuzhi_url'])
            taskids.append(d['taskId'])
        
        time.sleep(5)
        if batch:
            batchinfo = mfcad_lite.QueryBatchTask(datainfo['data']['batchNO'])
            for d in batchinfo['data']['childList']:
                taskids.append(d['taskId'])

        while True:
            taskinfo = mfcad_lite.QueryTaskDetail(datainfo['data']['taskID'])
            if int(taskinfo['data']['status']) == 2:
                result = update_isedrawings(n, 2, tudata['tuid'])
                break
            elif int(taskinfo['data']['status']) > 3:
                update_isedrawings(n, 3, tudata['tuid'])
                break
            else:
                print('程序转换中,继续等待2秒进行下次轮询...')
                time.sleep(2)
                continue
        update_task(result, tudata['tuid'])
            



if __name__ == '__main__':
    while True:
        listAll(bucket_name)
        print('30秒钟后重新开始扫描！')
        time.sleep(30)
