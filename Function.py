import time, datetime, hashlib, random
import zipfile, rarfile, py7zr
import urllib.request, json, socket
import sys, os, shutil, errno, re
from qiniu import etag
from qiniu import Auth
from qiniu import BucketManager
from qcloud_cos import CosConfig
from qcloud_cos import CosS3Client
from subprocess import Popen, PIPE, STDOUT
from qcloud_cos.cos_exception import CosClientError, CosServiceError
from io import BytesIO  # py3


# 30秒后就会超时
socket.setdefaulttimeout(30)

bytes_chr = lambda c: bytes([c])

# qiniu官方库中的etag函数对4m以上的文件出错，下面的是qetag工具
CHUNK_BITS = 22
CHUNK_SIZE = 1 << CHUNK_BITS  # == 2 ** 22 == 4 * 1024 * 1024 == 4MiB

# 上传文件到cos
def cos_upload(secret_id,secret_key,bucket_name,local_file,key):
    region = 'ap-beijing'      # 替换为用户的 region，已创建桶归属的 region 可以在控制台查看，https://console.cloud.tencent.com/cos5/bucket
                            # COS 支持的所有 region 列表参见 https://cloud.tencent.com/document/product/436/6224
    token = None               # 如果使用永久密钥不需要填入 token，如果使用临时密钥需要填入，临时密钥生成和使用指引参见 https://cloud.tencent.com/document/product/436/14048
    scheme = 'https'           # 指定使用 http/https 协议来访问 COS，默认为 https，可不填

    config = CosConfig(Region=region, SecretId=secret_id, SecretKey=secret_key, Token=token, Scheme=scheme)
    client = CosS3Client(config)

    #### 高级上传接口（推荐）
    # 根据文件大小自动选择简单上传或分块上传，分块上传具备断点续传功能。
    try:
        response = client.upload_file(
            Bucket=bucket_name,
            LocalFilePath=local_file,
            Key=key,
            PartSize=1,
            MAXThread=10,
            EnableMD5=False
        )
        return True
    except:
        return False


# 文件单位换算
def getSizeInNiceString(sizeInBytes):
    """
    Convert the given byteCount into a string like: 9.9bytes/KB/MB/GB
    """
    for (cutoff, label) in [(1024 * 1024 * 1024, "GB"),
                            (1024 * 1024, "MB"),
                            (1024, "KB"),
                            ]:
        if sizeInBytes >= cutoff:
            return "%.1f %s" % (sizeInBytes * 1.0 / cutoff, label)

    if sizeInBytes == 1:
        return "1 byte"
    else:
        bytes = "%.1f" % (sizeInBytes or 0,)
        return (bytes[:-2] if bytes.endswith('.0') else bytes) + ' bytes'
    
# 下载进度条
def report(count, blockSize, totalSize):
    percent = int(count * blockSize * 100 / totalSize)
    sys.stdout.write('\r' + "%s,  %d%%" % (getSizeInNiceString(totalSize), percent) + ' complete')
    sys.stdout.flush()



def ensure_bytes(text, encoding='U8'):
    return text if isinstance(text, bytes) else text.encode(encoding)


def get_io_size(fio):
    """get file size from fio"""
    fio.seek(0, os.SEEK_END)
    fsize = fio.tell()
    fio.seek(0)
    return fsize


def get_io_qetag(fio):
    """Caculates qetag from file object
    Parameters:
        - fio: file-like object to the file
    Usage:
    >>> data = bytes_chr(0) * (CHUNK_SIZE + 42) * 42
    >>> fio = BytesIO(data)
    >>> print(get_io_qetag(fio))
    lnmoz9lrkr6HWgZyTqu2vD0XUj6R
    Returns qetag
    """
    size = get_io_size(fio)
    flag = CHUNK_BITS
    sha1 = hashlib.sha1
    buf = []
    while size > 0:
        size -= CHUNK_SIZE
        buf.append(sha1(fio.read(CHUNK_SIZE)).digest())
    buf = b''.join(buf)
    if len(buf) > 20:  # more than 1 chunk
        flag |= 0x80
        buf = sha1(buf).digest()
    fio.seek(0)
    return base64.urlsafe_b64encode(bytes_chr(flag) + buf).decode('ASCII')


def get_qetag(filename):
    """Caculates qetag
    Parameters:
        - filename: string, file name
    Returns qetag
    """
    with open(filename, 'rb') as fp:
        return get_io_qetag(fp)


####qetag工具结束
def custom_popen(cmd):
    """Disconnect cmd from parent fds, read only from stdout."""
    print(cmd)
    # needed for py2exe
    creationflags = 0
    if sys.platform == 'win32':
        creationflags = 0x08000000  # CREATE_NO_WINDOW

    # run command
    try:
        p = Popen(cmd, bufsize=0,
                  stdout=PIPE, stdin=PIPE, stderr=STDOUT,
                  creationflags=creationflags)
    except OSError:
        ex = sys.exc_info()[1]
        if ex.errno == errno.ENOENT:
            raise
        raise
    return p


def get_unixtime():
    return int(time.time())



# 下载单个七牛文件
def get_file_from_qiniu(access_key, secret_key, bucket_domain, downDir, bucket_name, key, tuid):
    # 初始化Auth状态
    q = Auth(access_key, secret_key)

    # 初始化BucketManager
    bucket = BucketManager(q)
    target = downDir + '/' + key

    # 获取本地文件的基本信息
    ret, info = bucket.stat(bucket_name, key)
    print(info)
    if ret == None:
        print('获取本地文件的基本信息:%s-%s', tuid, key)
        return
    print(time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(time.time())))

    dir = os.path.dirname(os.path.abspath(target))
    if not os.path.isdir(dir):
        print('[mkdir_p]', dir)
        try:
            os.makedirs(dir)
        except OSError as exc:  # Python >2.5
            if exc.errno == errno.EEXIST and os.path.isdir(dir):
                pass
            else:
                print('创建文件目录失败:%s', dir)
                return

    # 本地文件如果存在，获取七牛文件信息;如果文件信息一致，跳过；如果不一致备份原文件，并重新下载

    base_url = 'http://%s/%s' % (bucket_domain, key)
    url = q.private_download_url(base_url, expires=36000)
    print(target)
    file_status = True
    if os.path.exists(target):
        local_etag = get_qetag(target)
        print(local_etag)
        if ret['fsize'] == 0:
           print('七牛文件大小为0，跳过:%s-%s', tuid, target)
           file_status = False
        elif local_etag == ret['hash']:
           print('本地和服务器一致，不用重新下载:%s-%s', tuid, target)
        else:
           try:
               urllib.request.urlretrieve(url, target, reporthook=report)
               print(url)
               local_etag = get_qetag(target)
               if local_etag != ret['hash']:
                   print('重新下载文件失败，hash校验错误:%s - %s', tuid, target)
                   file_status = False
           except:
               print('重新下载文件失败，其他错误:%s - %s', tuid, target)
               file_status = False
    else:
        try:
            urllib.request.urlretrieve(url, target, reporthook=report)
            local_etag = get_qetag(target)
            if local_etag != ret['hash']:
                print('首次下载文件失败，hash校验错误:%s - %s', tuid, target)
                file_status = False
        except:
            print('首次下载文件失败，其他错误:%s - %s ', tuid, target)
            file_status = False

    return {'tuzhi_url': 'http://tu.mfcad.com/tuzhi/'+key, 'file_status': file_status}



#判断文件名是否是压缩包
def iszip(filename):
    _ , sufix = os.path.splitext(filename)
    sufix = sufix.lower()

    if sufix == '.zip' or sufix == '.rar' or sufix == '.7z':
        return True
    else:
        return False