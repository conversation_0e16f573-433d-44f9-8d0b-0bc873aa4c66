# -*- coding: utf-8 -*-
import pymysql
import warnings

# 将warning等警告信息也中断
warnings.filterwarnings('error', category=pymysql.Warning)


class MySQL:
    def __init__(self, host, user, password, port=3306, charset="utf8"):
        self.host = host
        self.port = port
        self.user = user
        self.password = password
        self.charset = charset
        try:
            self.conn = pymysql.connect(host=self.host, port=self.port, user=self.user, passwd=self.password,
                                        charset=self.charset)
            self.conn.autocommit_mode = False
            # self.conn.set_character_set(self.charset)
            self.cur = self.conn.cursor()
        except pymysql.Error as e:
            print("Mysql Error %d: %s" % (e.args[0], e.args[1]))

    def __del__(self):
        self.close()

    def selectDb(self, db):
        try:
            self.conn.select_db(db)
        except pymysql.Error as e:
            print("Mysql Error %d: %s" % (e.args[0], e.args[1]))

    def query(self, sql):
        try:
            print(sql)
            n = self.cur.execute(sql)
            return n
        except pymysql.Error as e:
            if self.charset == 'utf8':
                print("Mysql Error:%s\nSQL:%s" % (e, sql.encode('utf-8').decode('gbk')))
            else:
                print("Mysql Error:%s\nSQL:%s" % (e, sql))
        except pymysql.Warning as e:
            if self.charset == 'utf8':
                print("Mysql Error:%s\nSQL:%s" % (e, sql.encode('utf-8').decode('gbk')))
            else:
                print("Mysql Error:%s\nSQL:%s" % (e, sql))

    def fetchRow(self):
        result = self.cur.fetchone()
        return result

    def fetchAll(self):
        result = self.cur.fetchall()
        desc = self.cur.description
        d = []

        for inv in result:
            _d = {}
            for i in range(0, len(inv)):
                _d[desc[i][0]] = str(inv[i])
            d.append(_d)
        return d

    def insert(self, table_name, data):
        columns = data.keys()
        _prefix = "".join(['INSERT INTO `', table_name, '`'])
        _fields = ",".join(["".join(['`', column, '`']) for column in columns])
        _values = ",".join(["%s" for i in range(len(columns))])
        _sql = "".join([_prefix, "(", _fields, ") VALUES (", _values, ")"])
        _params = [data[key] for key in columns]
        print(_sql, tuple(_params))
        return self.cur.execute(_sql, tuple(_params))

    def update(self, tbname, data, condition):
        _fields = []
        _prefix = "".join(['UPDATE `', tbname, '`', 'SET'])
        for key in data.keys():
            _fields.append("`%s` = '%s'" % (key, data[key]))

        _sql = " ".join([_prefix, ",".join(_fields), "WHERE ", condition])
        print(_sql)
        return self.cur.execute(_sql)

    def delete(self, tbname, condition):
        _prefix = "".join(['DELETE FROM  `', tbname, '`', 'WHERE'])
        _sql = "".join([_prefix, condition])
        print(_sql)
        return self.cur.execute(_sql)

    def getLastInsertId(self):
        return self.cur.lastrowid

    def rowcount(self):
        return self.cur.rowcount

    def commit(self):
        self.conn.commit()

    def rollback(self):
        self.conn.rollback()

    def close(self):
        try:
            self.cur.close
            self.conn.close
        except pymysql.Error as e:
            print("Mysql Error %d: %s" % (e.args[0], e.args[1]))
        # self.conn.close
        # self.cur.close

# if __name__=='__main__':
#     n = MySQL('192.168.1.100', 'root', '123456', 3306)
#     n.selectDb('search_mfcad_com')
#
#     last =1
#     while True:
#         n = MySQL('192.168.1.100', 'root', '123456', 3306)
#         n.selectDb('search_mfcad_com')
#         if last == 1:
#             sql = "SELECT id,fenci FROM v9_search_keyword order by id asc limit 100"
#             n.query(sql)
#             arrs = n.fetchAll()
#         else:
#             sql = "SELECT id,fenci FROM v9_search_keyword where id > "+str(last)+" order by id asc limit 100"
#             n.query(sql)
#             arrs = n.fetchAll()
#         arr=[]
#         wordarr = ['Array', 'proe', 'CATLA', 'SolidWorks', 'creo', '图纸', '模型', '设计', '一套', '三维', '二维', '一款', '10款',
#                     '设备', '总图', '一种', 'X678', '?', '(', ')']
#         if arrs:
#            for a in arrs:
#                if a['fenci']:
#                    array=a['fenci'].split('|')
#                    for ar in array:
#                        if ar not in wordarr:
#                             if len(ar) >1 and len(ar) <6:
#                                 sql = "SELECT count(*) as num FROM v9_search_key where keyword='"+str(ar)+"'"
#                                 n.query(sql)
#                                 num = n.fetchAll()[0]['num']
#                                 if int(num)  == 0:
#                                     n.insert('v9_search_key',{'keyword':ar})
#                                 else:
#                                     count=int(num)+1
#                                     n.update('v9_search_key',{'count':count},'keyword="'+str(ar)+'"')
#                                 n.commit()
#                        else:
#                            print(str(ar)+'存在数组中')
#                last=a['id']
#         n.close()